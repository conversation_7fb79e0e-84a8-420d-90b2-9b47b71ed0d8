from contextlib import asynccontextmanager

from api.controllers import annotation_tasks, eval_controller, xml_infer
from api.db.database import close_db, init_db
from api.middleware.error_handler import (
    general_exception_handler,
    http_exception_handler,
    validation_exception_handler,
)
from api.models.response import StandardResponse
from api.utils.svrkit.ubf import preload_location_data
from fastapi import FastAPI, HTTPException
from fastapi.exceptions import RequestValidationError


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动数据库
    await init_db(
        "postgresql+asyncpg://admin:Wxassistant_20141213@21.87.161.10/brightfu"
    )
    # 在模块导入时预加载地理位置数据
    preload_location_data()
    yield
    # 关闭数据库
    await close_db()


app = FastAPI(lifespan=lifespan)

# 注册异常处理器
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

app.include_router(annotation_tasks.router, prefix="/annotations/tasks")
app.include_router(xml_infer.router, prefix="/infer")
app.include_router(eval_controller.router, prefix="/evals")


@app.get("/")
@app.head("/")
async def root():
    return StandardResponse(data={"status": "ok"})


@app.get("/health")
async def health():
    return StandardResponse(data="OK")


if __name__ == "__main__":
    import sys

    import uvicorn

    # 直接运行时使用单进程模式，避免与 Gunicorn 冲突
    uvicorn.run("app:app", host="0.0.0.0", port=int(sys.argv[1]))
