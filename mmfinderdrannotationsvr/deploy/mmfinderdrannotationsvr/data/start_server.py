#!/usr/bin/env python3
"""
服务器启动脚本
支持开发模式和生产模式
"""
import argparse
import os
import subprocess
import sys


def start_development(port: int):
    """开发模式启动 - 使用 uvicorn 直接启动"""
    print(f"Starting development server on port {port}")
    import uvicorn
    uvicorn.run("app:app", host="0.0.0.0", port=port, reload=True)


def start_production(port: int, workers: int = 8):
    """生产模式启动 - 使用 Gunicorn + Uvicorn Worker"""
    print(f"Starting production server with {workers} workers on port {port}")
    cmd = [
        "gunicorn",
        "-w", str(workers),
        "-k", "uvicorn.workers.UvicornWorker",
        "--bind", f"0.0.0.0:{port}",
        "--access-logfile", "-",
        "--error-logfile", "-",
        "app:app"
    ]
    subprocess.run(cmd)


def main():
    parser = argparse.ArgumentParser(description="启动 FastAPI 服务器")
    parser.add_argument("port", type=int, help="服务器端口")
    parser.add_argument(
        "--mode", 
        choices=["dev", "prod"], 
        default="prod",
        help="启动模式: dev (开发模式) 或 prod (生产模式)"
    )
    parser.add_argument(
        "--workers", 
        type=int, 
        default=8,
        help="生产模式下的 worker 数量 (默认: 8)"
    )
    
    args = parser.parse_args()
    
    if args.mode == "dev":
        start_development(args.port)
    else:
        start_production(args.port, args.workers)


if __name__ == "__main__":
    main()
